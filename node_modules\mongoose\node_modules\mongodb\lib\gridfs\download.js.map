{"version": 3, "file": "download.js", "sourceRoot": "", "sources": ["../../src/gridfs/download.ts"], "names": [], "mappings": ";;;AAAA,mCAAkC;AAKlC,oCAKkB;AAgElB;;;;;GAKG;AACH,MAAa,sBAAuB,SAAQ,iBAAQ;IA8BlD;;;;;;OAMG;IACH,YACE,MAA+B,EAC/B,KAA6B,EAC7B,cAA0C,EAC1C,MAAgB,EAChB,OAAuC;QAEvC,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,CAAC,GAAG;YACP,WAAW,EAAE,CAAC;YACd,WAAW,EAAE,CAAC;YACd,SAAS,EAAE,CAAC;YACZ,MAAM;YACN,QAAQ,EAAE,CAAC;YACX,KAAK;YACL,MAAM;YACN,IAAI,EAAE,KAAK;YACX,WAAW,EAAE,CAAC;YACd,OAAO,EAAE;gBACP,KAAK,EAAE,CAAC;gBACR,GAAG,EAAE,CAAC;gBACN,GAAG,OAAO;aACX;YACD,cAAc;SACf,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACM,KAAK;QACZ,IAAI,IAAI,CAAC,SAAS;YAAE,OAAO;QAC3B,WAAW,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;IACxC,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,KAAK,GAAG,CAAC;QACb,kBAAkB,CAAC,IAAI,CAAC,CAAC;QACzB,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;QAC7B,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;;OAMG;IACH,GAAG,CAAC,GAAG,GAAG,CAAC;QACT,kBAAkB,CAAC,IAAI,CAAC,CAAC;QACzB,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,GAAG,GAAG,CAAC;QACzB,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,KAAK;QACT,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAChB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,IAAI,CAAC,CAAC,CAAC,MAAM,EAAE;YACjB,IAAI;gBACF,MAAM,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;aAC7B;oBAAS;gBACR,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAC;aACzC;SACF;aAAM;YACL,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE;gBAChB,6DAA6D;gBAC7D,eAAe;gBACf,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAC;aACzC;SACF;IACH,CAAC;;AArHD;;;GAGG;AACa,4BAAK,GAAG,OAAgB,CAAC;AACzC;;;GAGG;AACa,2BAAI,GAAG,MAAe,CAAC;AACvC;;;GAGG;AACa,2BAAI,GAAG,MAAe,CAAC;AACvC;;;GAGG;AACa,0BAAG,GAAG,KAAc,CAAC;AACrC;;;GAGG;AACa,4BAAK,GAAG,OAAgB,CAAC;AA5B9B,wDAAsB;AA4HnC,SAAS,kBAAkB,CAAC,MAA8B;IACxD,IAAI,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE;QACjB,MAAM,IAAI,8BAAsB,CAAC,2DAA2D,CAAC,CAAC;KAC/F;AACH,CAAC;AAED,SAAS,MAAM,CAAC,MAA8B;IAC5C,IAAI,MAAM,CAAC,SAAS;QAAE,OAAO;IAC7B,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM;QAAE,OAAO;IAC7B,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI;QAAE,OAAO;IAE3B,MAAM,gBAAgB,GAAG,CAAC,EACxB,KAAK,EACL,GAAG,EACqD,EAAE,EAAE;QAC5D,IAAI,MAAM,CAAC,SAAS,EAAE;YACpB,OAAO;SACR;QACD,IAAI,KAAK,EAAE;YACT,MAAM,CAAC,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;YACjD,OAAO;SACR;QACD,IAAI,CAAC,GAAG,EAAE;YACR,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAElB,MAAM,CAAC,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,CAAC,IAAI,CAC3B,GAAG,EAAE;gBACH,MAAM,CAAC,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAC;YAC5C,CAAC,EACD,KAAK,CAAC,EAAE;gBACN,MAAM,CAAC,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;YACnD,CAAC,CACF,CAAC;YACF,OAAO;SACR;QAED,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI;YAAE,OAAO;QAE3B,MAAM,cAAc,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC;QACjE,MAAM,SAAS,GAAG,MAAM,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;QACtC,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;QACzE,IAAI,GAAG,CAAC,CAAC,GAAG,SAAS,EAAE;YACrB,OAAO,MAAM,CAAC,IAAI,CAChB,sBAAsB,CAAC,KAAK,EAC5B,IAAI,6BAAqB,CACvB,qCAAqC,GAAG,CAAC,CAAC,eAAe,SAAS,EAAE,CACrE,CACF,CAAC;SACH;QAED,IAAI,GAAG,CAAC,CAAC,GAAG,SAAS,EAAE;YACrB,OAAO,MAAM,CAAC,IAAI,CAChB,sBAAsB,CAAC,KAAK,EAC5B,IAAI,6BAAqB,CAAC,iCAAiC,GAAG,CAAC,CAAC,eAAe,SAAS,EAAE,CAAC,CAC5F,CAAC;SACH;QAED,IAAI,GAAG,GAAG,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC;QAEjE,IAAI,GAAG,CAAC,UAAU,KAAK,cAAc,EAAE;YACrC,IAAI,cAAc,IAAI,CAAC,EAAE;gBACvB,OAAO,MAAM,CAAC,IAAI,CAChB,sBAAsB,CAAC,KAAK,EAC5B,IAAI,6BAAqB,CACvB,iCAAiC,GAAG,CAAC,CAAC,0BAA0B,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,2BAA2B,MAAM,CAAC,CAAC,CAAC,SAAS,QAAQ,CAC1I,CACF,CAAC;aACH;YAED,OAAO,MAAM,CAAC,IAAI,CAChB,sBAAsB,CAAC,KAAK,EAC5B,IAAI,6BAAqB,CACvB,4CAA4C,GAAG,CAAC,UAAU,eAAe,cAAc,EAAE,CAC1F,CACF,CAAC;SACH;QAED,MAAM,CAAC,CAAC,CAAC,SAAS,IAAI,GAAG,CAAC,UAAU,CAAC;QAErC,IAAI,GAAG,CAAC,UAAU,KAAK,CAAC,EAAE;YACxB,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SAC1B;QAED,IAAI,UAAU,GAAG,IAAI,CAAC;QACtB,IAAI,QAAQ,GAAG,IAAI,CAAC;QAEpB,IAAI,MAAM,CAAC,CAAC,CAAC,WAAW,IAAI,IAAI,EAAE;YAChC,UAAU,GAAG,MAAM,CAAC,CAAC,CAAC,WAAW,CAAC;YAClC,MAAM,CAAC,CAAC,CAAC,WAAW,GAAG,CAAC,CAAC;SAC1B;QAED,MAAM,aAAa,GAAG,SAAS,KAAK,MAAM,CAAC,CAAC,CAAC,WAAW,GAAG,CAAC,CAAC;QAC7D,MAAM,eAAe,GAAG,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,WAAW,CAAC;QACpE,IAAI,aAAa,IAAI,MAAM,CAAC,CAAC,CAAC,WAAW,IAAI,IAAI,EAAE;YACjD,QAAQ,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,CAAC,CAAC,WAAW,CAAC;SAC3D;aAAM,IAAI,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,IAAI,eAAe,GAAG,GAAG,CAAC,IAAI,CAAC,UAAU,EAAE;YACxE,QAAQ,GAAG,eAAe,CAAC;SAC5B;QAED,IAAI,UAAU,IAAI,IAAI,IAAI,QAAQ,IAAI,IAAI,EAAE;YAC1C,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,UAAU,IAAI,CAAC,EAAE,QAAQ,IAAI,GAAG,CAAC,UAAU,CAAC,CAAC;SAC9D;QAED,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACjB,OAAO;IACT,CAAC,CAAC;IAEF,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,IAAI,CACzB,GAAG,CAAC,EAAE,CAAC,gBAAgB,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,EAC7C,KAAK,CAAC,EAAE,CAAC,gBAAgB,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC,CAChD,CAAC;AACJ,CAAC;AAED,SAAS,IAAI,CAAC,MAA8B;IAC1C,MAAM,cAAc,GAAgB,EAAE,CAAC;IACvC,IAAI,MAAM,CAAC,CAAC,CAAC,cAAc,EAAE;QAC3B,cAAc,CAAC,cAAc,GAAG,MAAM,CAAC,CAAC,CAAC,cAAc,CAAC;KACzD;IACD,IAAI,MAAM,CAAC,CAAC,CAAC,OAAO,IAAI,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE;QAC7C,cAAc,CAAC,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC;KAC7C;IACD,IAAI,MAAM,CAAC,CAAC,CAAC,OAAO,IAAI,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE;QAC7C,cAAc,CAAC,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC;KAC7C;IAED,MAAM,gBAAgB,GAAG,CAAC,EACxB,KAAK,EACL,GAAG,EACqD,EAAE,EAAE;QAC5D,IAAI,KAAK,EAAE;YACT,OAAO,MAAM,CAAC,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;SACzD;QAED,IAAI,CAAC,GAAG,EAAE;YACR,MAAM,UAAU,GAAG,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG;gBACpC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE;gBAChC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC;YAC7B,MAAM,MAAM,GAAG,sBAAsB,UAAU,gBAAgB,CAAC;YAChE,kBAAkB;YAClB,MAAM,GAAG,GAAG,IAAI,yBAAiB,CAAC,MAAM,CAAC,CAAC;YAC1C,GAAG,CAAC,IAAI,GAAG,QAAQ,CAAC,CAAC,sDAAsD;YAC3E,OAAO,MAAM,CAAC,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;SACvD;QAED,8DAA8D;QAC9D,oBAAoB;QACpB,IAAI,GAAG,CAAC,MAAM,IAAI,CAAC,EAAE;YACnB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClB,OAAO;SACR;QAED,IAAI,MAAM,CAAC,SAAS,EAAE;YACpB,4DAA4D;YAC5D,iEAAiE;YACjE,kBAAkB;YAClB,MAAM,CAAC,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAC;YAC1C,OAAO;SACR;QAED,IAAI;YACF,MAAM,CAAC,CAAC,CAAC,WAAW,GAAG,iBAAiB,CAAC,MAAM,EAAE,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;SACzE;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,MAAM,CAAC,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;SACzD;QAED,MAAM,MAAM,GAAa,EAAE,QAAQ,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC;QAE/C,sEAAsE;QACtE,8EAA8E;QAC9E,+CAA+C;QAC/C,IAAI,MAAM,CAAC,CAAC,CAAC,OAAO,IAAI,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,IAAI,IAAI,EAAE;YACtD,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC;YAChE,IAAI,IAAI,GAAG,CAAC,EAAE;gBACZ,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;aAC9B;SACF;QACD,MAAM,CAAC,CAAC,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAE9D,IAAI,MAAM,CAAC,CAAC,CAAC,cAAc,EAAE;YAC3B,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC;SAC7D;QAED,MAAM,CAAC,CAAC,CAAC,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC;QAC7D,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,GAAiB,CAAC;QAElC,IAAI;YACF,MAAM,CAAC,CAAC,CAAC,WAAW,GAAG,eAAe,CAAC,MAAM,EAAE,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;SACxF;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,MAAM,CAAC,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;SACzD;QAED,MAAM,CAAC,IAAI,CAAC,sBAAsB,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;QAC9C,OAAO;IACT,CAAC,CAAC;IAEF,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC,IAAI,CAC1D,GAAG,CAAC,EAAE,CAAC,gBAAgB,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,EAC7C,KAAK,CAAC,EAAE,CAAC,gBAAgB,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC,CAChD,CAAC;AACJ,CAAC;AAED,SAAS,WAAW,CAAC,MAA8B,EAAE,QAAkB;IACrE,IAAI,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE;QACjB,OAAO,QAAQ,EAAE,CAAC;KACnB;IAED,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE;QAClB,IAAI,CAAC,MAAM,CAAC,CAAC;QACb,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC;KACtB;IAED,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE;QACvB,QAAQ,EAAE,CAAC;IACb,CAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,iBAAiB,CACxB,MAA8B,EAC9B,GAAa,EACb,OAAsC;IAEtC,IAAI,OAAO,IAAI,OAAO,CAAC,KAAK,IAAI,IAAI,EAAE;QACpC,IAAI,OAAO,CAAC,KAAK,GAAG,GAAG,CAAC,MAAM,EAAE;YAC9B,MAAM,IAAI,iCAAyB,CACjC,iBAAiB,OAAO,CAAC,KAAK,mDAAmD,GAAG,CAAC,MAAM,GAAG,CAC/F,CAAC;SACH;QACD,IAAI,OAAO,CAAC,KAAK,GAAG,CAAC,EAAE;YACrB,MAAM,IAAI,iCAAyB,CAAC,iBAAiB,OAAO,CAAC,KAAK,wBAAwB,CAAC,CAAC;SAC7F;QACD,IAAI,OAAO,CAAC,GAAG,IAAI,IAAI,IAAI,OAAO,CAAC,GAAG,GAAG,OAAO,CAAC,KAAK,EAAE;YACtD,MAAM,IAAI,iCAAyB,CACjC,iBAAiB,OAAO,CAAC,KAAK,0CAA0C,OAAO,CAAC,GAAG,GAAG,CACvF,CAAC;SACH;QAED,MAAM,CAAC,CAAC,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,GAAG,GAAG,CAAC,SAAS,CAAC,GAAG,GAAG,CAAC,SAAS,CAAC;QAC/E,MAAM,CAAC,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC;QAE9D,OAAO,OAAO,CAAC,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC;KAC3C;IACD,MAAM,IAAI,iCAAyB,CAAC,8BAA8B,CAAC,CAAC;AACtE,CAAC;AAED,SAAS,eAAe,CACtB,MAA8B,EAC9B,GAAa,EACb,MAA+B,EAC/B,OAAsC;IAEtC,IAAI,OAAO,IAAI,OAAO,CAAC,GAAG,IAAI,IAAI,EAAE;QAClC,IAAI,OAAO,CAAC,GAAG,GAAG,GAAG,CAAC,MAAM,EAAE;YAC5B,MAAM,IAAI,iCAAyB,CACjC,eAAe,OAAO,CAAC,GAAG,mDAAmD,GAAG,CAAC,MAAM,GAAG,CAC3F,CAAC;SACH;QACD,IAAI,OAAO,CAAC,KAAK,IAAI,IAAI,IAAI,OAAO,CAAC,KAAK,GAAG,CAAC,EAAE;YAC9C,MAAM,IAAI,iCAAyB,CAAC,eAAe,OAAO,CAAC,GAAG,wBAAwB,CAAC,CAAC;SACzF;QAED,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAEpF,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,GAAG,GAAG,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC,CAAC;QAE7D,MAAM,CAAC,CAAC,CAAC,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC;QAE9D,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,GAAG,GAAG,CAAC,SAAS,CAAC,GAAG,GAAG,CAAC,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC;KAC7E;IACD,MAAM,IAAI,iCAAyB,CAAC,4BAA4B,CAAC,CAAC;AACpE,CAAC"}