#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const readline = require('readline');

console.log('🚀 Heroku Deployment Script for CTF Challenge');
console.log('==============================================');

// Check if .env exists
if (!fs.existsSync('.env')) {
    console.error('❌ .env file not found. Please run "npm run setup" first.');
    process.exit(1);
}

// Load environment variables
require('dotenv').config();

if (!process.env.FLAG) {
    console.error('❌ FLAG environment variable not set in .env file.');
    process.exit(1);
}

console.log('✅ Environment variables validated');

const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

function askQuestion(question) {
    return new Promise((resolve) => {
        rl.question(question, (answer) => {
            resolve(answer.trim());
        });
    });
}

async function deployToHeroku() {
    try {
        // Check if Heroku CLI is installed
        try {
            execSync('heroku --version', { stdio: 'pipe' });
            console.log('✅ Heroku CLI found');
        } catch (error) {
            console.error('❌ Heroku CLI not found. Please install it first:');
            console.log('   npm install -g heroku');
            process.exit(1);
        }

        // Check if user is logged in
        try {
            execSync('heroku auth:whoami', { stdio: 'pipe' });
            console.log('✅ Heroku authentication verified');
        } catch (error) {
            console.log('🔐 Please login to Heroku...');
            execSync('heroku login', { stdio: 'inherit' });
        }

        // Ask for app name
        const appName = await askQuestion('Enter your Heroku app name (or press Enter for auto-generated): ');
        
        let createCommand = 'heroku create';
        if (appName) {
            createCommand += ` ${appName}`;
        }

        // Create Heroku app
        console.log('🔄 Creating Heroku app...');
        try {
            const output = execSync(createCommand, { encoding: 'utf8' });
            console.log('✅ Heroku app created successfully');
            console.log(output);
        } catch (error) {
            if (error.message.includes('already exists')) {
                console.log('⚠️  App already exists, continuing with deployment...');
            } else {
                throw error;
            }
        }

        // Set environment variables
        console.log('🔄 Setting environment variables...');
        
        const envVars = [
            `MONGODB_URI="${process.env.MONGODB_URI}"`,
            `FLAG="${process.env.FLAG}"`,
            `PORT="${process.env.PORT || 3000}"`
        ];

        for (const envVar of envVars) {
            try {
                execSync(`heroku config:set ${envVar}`, { stdio: 'pipe' });
                console.log(`✅ Set ${envVar.split('=')[0]}`);
            } catch (error) {
                console.error(`❌ Failed to set ${envVar.split('=')[0]}`);
                throw error;
            }
        }

        // Create Procfile if it doesn't exist
        if (!fs.existsSync('Procfile')) {
            console.log('📝 Creating Procfile...');
            fs.writeFileSync('Procfile', 'web: node server.js\n');
            console.log('✅ Procfile created');
        }

        // Initialize git if needed
        if (!fs.existsSync('.git')) {
            console.log('🔄 Initializing git repository...');
            execSync('git init', { stdio: 'inherit' });
            execSync('git add .', { stdio: 'inherit' });
            execSync('git commit -m "Initial commit"', { stdio: 'inherit' });
        }

        // Deploy to Heroku
        console.log('🚀 Deploying to Heroku...');
        execSync('git add .', { stdio: 'inherit' });
        
        try {
            execSync('git commit -m "Deploy CTF challenge to Heroku"', { stdio: 'inherit' });
        } catch (error) {
            console.log('⚠️  No changes to commit, proceeding with existing commit...');
        }

        // Add Heroku remote if it doesn't exist
        try {
            execSync('git remote get-url heroku', { stdio: 'pipe' });
        } catch (error) {
            console.log('🔗 Adding Heroku remote...');
            const appNameFromGit = appName || 'your-app-name';
            execSync(`heroku git:remote -a ${appNameFromGit}`, { stdio: 'inherit' });
        }

        execSync('git push heroku main', { stdio: 'inherit' });

        console.log('✅ Deployment completed successfully!');
        
        // Get app URL
        try {
            const appUrl = execSync('heroku apps:info --json', { encoding: 'utf8' });
            const appInfo = JSON.parse(appUrl);
            console.log(`🌐 Your CTF challenge is live at: ${appInfo.app.web_url}`);
        } catch (error) {
            console.log('🌐 Your app should be live. Run "heroku open" to view it.');
        }

        // Setup database
        const setupDb = await askQuestion('Do you want to setup the database now? (y/n): ');
        if (setupDb.toLowerCase() === 'y' || setupDb.toLowerCase() === 'yes') {
            console.log('🔄 Setting up database...');
            execSync('heroku run npm run setup', { stdio: 'inherit' });
            console.log('✅ Database setup completed');
        }

        console.log('\n🎯 Deployment Summary:');
        console.log('- Heroku app created and configured');
        console.log('- Environment variables set');
        console.log('- Application deployed');
        console.log('- Database ready (if setup was run)');
        console.log('\n💡 Useful commands:');
        console.log('- heroku logs --tail (view logs)');
        console.log('- heroku open (open app in browser)');
        console.log('- heroku run npm run flag:list (manage flags)');

    } catch (error) {
        console.error('❌ Deployment failed:', error.message);
        process.exit(1);
    } finally {
        rl.close();
    }
}

// Run deployment
deployToHeroku();
