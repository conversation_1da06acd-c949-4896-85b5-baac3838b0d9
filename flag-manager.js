const mongoose = require('mongoose');
require('dotenv').config();

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb+srv://tamils343435:<EMAIL>/ctf_bank?retryWrites=true&w=majority&appName=Cluster0';

const flagSchema = new mongoose.Schema({
    name: String,
    value: String,
    description: String,
    created_at: { type: Date, default: Date.now }
});

const Flag = mongoose.model('Flag', flagSchema);

async function setFlag(flagName, flagValue, description = '') {
    try {
        console.log('🔄 Connecting to MongoDB...');
        await mongoose.connect(MONGODB_URI);
        
        const result = await Flag.findOneAndUpdate(
            { name: flagName },
            { 
                value: flagValue, 
                description: description,
                created_at: new Date()
            },
            { upsert: true, new: true }
        );
        
        console.log(`✅ Flag '${flagName}' set successfully!`);
        console.log(`   Value: ${flagValue}`);
        console.log(`   Description: ${description}`);
        return result;
    } catch (error) {
        console.error('❌ Error setting flag:', error.message);
        throw error;
    } finally {
        await mongoose.disconnect();
        console.log('🔌 Disconnected from MongoDB');
    }
}

async function getFlag(flagName) {
    try {
        console.log('🔄 Connecting to MongoDB...');
        await mongoose.connect(MONGODB_URI);
        const flag = await Flag.findOne({ name: flagName });
        
        if (flag) {
            console.log(`🚩 Flag '${flagName}' found:`);
            console.log(`   Value: ${flag.value}`);
            console.log(`   Description: ${flag.description}`);
            console.log(`   Created: ${flag.created_at}`);
            return flag.value;
        } else {
            console.log(`❌ Flag '${flagName}' not found in database`);
            return null;
        }
    } catch (error) {
        console.error('❌ Error getting flag:', error.message);
        throw error;
    } finally {
        await mongoose.disconnect();
        console.log('🔌 Disconnected from MongoDB');
    }
}

async function listFlags() {
    try {
        console.log('🔄 Connecting to MongoDB...');
        await mongoose.connect(MONGODB_URI);
        const flags = await Flag.find({});
        
        if (flags.length > 0) {
            console.log(`📋 Found ${flags.length} flags:`);
            flags.forEach((flag, index) => {
                console.log(`\n${index + 1}. ${flag.name}`);
                console.log(`   Value: ${flag.value}`);
                console.log(`   Description: ${flag.description}`);
                console.log(`   Created: ${flag.created_at}`);
            });
        } else {
            console.log('❌ No flags found in database');
        }
        return flags;
    } catch (error) {
        console.error('❌ Error listing flags:', error.message);
        throw error;
    } finally {
        await mongoose.disconnect();
        console.log('🔌 Disconnected from MongoDB');
    }
}

async function deleteFlag(flagName) {
    try {
        console.log('🔄 Connecting to MongoDB...');
        await mongoose.connect(MONGODB_URI);
        const result = await Flag.findOneAndDelete({ name: flagName });
        
        if (result) {
            console.log(`✅ Flag '${flagName}' deleted successfully!`);
            console.log(`   Deleted value: ${result.value}`);
            return true;
        } else {
            console.log(`❌ Flag '${flagName}' not found`);
            return false;
        }
    } catch (error) {
        console.error('❌ Error deleting flag:', error.message);
        throw error;
    } finally {
        await mongoose.disconnect();
        console.log('🔌 Disconnected from MongoDB');
    }
}

function generateRandomFlag(prefix = 'WOLF') {
    const adjectives = ['hidden', 'secret', 'encrypted', 'protected', 'secure', 'stealth', 'shadow', 'phantom'];
    const nouns = ['data', 'treasure', 'key', 'code', 'mystery', 'vault', 'cipher', 'token'];
    const numbers = Math.floor(Math.random() * 9999).toString().padStart(4, '0');
    
    const adj = adjectives[Math.floor(Math.random() * adjectives.length)];
    const noun = nouns[Math.floor(Math.random() * nouns.length)];
    
    return `${prefix}{${adj}_${noun}_${numbers}}`;
}

async function rotateFlag(flagName = 'ctf_flag') {
    try {
        const newFlag = generateRandomFlag();
        await setFlag(flagName, newFlag, 'Auto-generated flag via rotation');
        console.log(`🔄 Flag '${flagName}' rotated successfully!`);
        return newFlag;
    } catch (error) {
        console.error('❌ Error rotating flag:', error.message);
        throw error;
    }
}

// Command line interface
if (require.main === module) {
    const args = process.argv.slice(2);
    const command = args[0];
    
    async function runCommand() {
        try {
            switch (command) {
                case 'set':
                    if (args.length >= 3) {
                        await setFlag(args[1], args[2], args[3] || '');
                    } else {
                        console.log('❌ Usage: node flag-manager.js set <flag_name> <flag_value> [description]');
                    }
                    break;
                    
                case 'get':
                    if (args.length >= 2) {
                        await getFlag(args[1]);
                    } else {
                        console.log('❌ Usage: node flag-manager.js get <flag_name>');
                    }
                    break;
                    
                case 'list':
                    await listFlags();
                    break;
                    
                case 'delete':
                    if (args.length >= 2) {
                        await deleteFlag(args[1]);
                    } else {
                        console.log('❌ Usage: node flag-manager.js delete <flag_name>');
                    }
                    break;
                    
                case 'rotate':
                    await rotateFlag(args[1] || 'ctf_flag');
                    break;
                    
                case 'generate':
                    const randomFlag = generateRandomFlag(args[1] || 'WOLF');
                    console.log(`🎲 Generated random flag: ${randomFlag}`);
                    break;
                    
                default:
                    console.log('🚀 CTF Flag Manager');
                    console.log('==================');
                    console.log('');
                    console.log('Available commands:');
                    console.log('  set <name> <value> [description]  - Set a flag value');
                    console.log('  get <name>                        - Get a flag value');
                    console.log('  list                              - List all flags');
                    console.log('  delete <name>                     - Delete a flag');
                    console.log('  rotate [name]                     - Rotate flag (default: ctf_flag)');
                    console.log('  generate [prefix]                 - Generate random flag');
                    console.log('');
                    console.log('Examples:');
                    console.log('  node flag-manager.js set ctf_flag "WOLF{my_custom_flag}"');
                    console.log('  node flag-manager.js get ctf_flag');
                    console.log('  node flag-manager.js list');
                    console.log('  node flag-manager.js rotate');
                    console.log('  node flag-manager.js generate WOLF');
            }
        } catch (error) {
            console.error('💥 Command failed:', error.message);
            process.exit(1);
        }
    }
    
    runCommand();
}

module.exports = { setFlag, getFlag, listFlags, deleteFlag, rotateFlag, generateRandomFlag };
