# MongoDB Flag Storage Setup Guide

## 🗄️ Database Structure for Flag Storage

### Collections Overview
Your CTF challenge uses two main MongoDB collections:

1. **`users`** - Stores user authentication data
2. **`flags`** - Stores challenge flags and decoy flags

### Flag Collection Schema
```javascript
{
  _id: ObjectId,
  name: String,        // Flag identifier (e.g., 'ctf_flag')
  value: String,       // Actual flag value (e.g., 'WOLF{your_flag}')
  description: String, // Flag description
  created_at: Date     // Timestamp
}
```

## 🚀 Step-by-Step Database Setup

### Method 1: Using Environment Variable (Recommended)

1. **Set your flag in .env file:**
```bash
# Edit .env file
FLAG=WOLF{your_custom_flag_here}
```

2. **Run the setup script:**
```bash
npm run setup
```

3. **Verify flag storage:**
```bash
# Connect to MongoDB and check
use ctf_bank
db.flags.find({name: 'ctf_flag'})
```

### Method 2: Manual MongoDB Setup

1. **Connect to your MongoDB Atlas cluster:**
```bash
# Using MongoDB Compass or mongo shell
mongodb+srv://tamils343435:<EMAIL>/ctf_bank
```

2. **Create the database and collections:**
```javascript
// Switch to ctf_bank database
use ctf_bank

// Create users collection
db.users.insertMany([
  {
    username: 'admin',
    password: 'admin123',
    email: '<EMAIL>',
    account_type: 'administrator',
    balance: 1000000,
    created_at: new Date()
  },
  {
    username: 'john_doe',
    password: 'password123',
    email: '<EMAIL>',
    account_type: 'premium',
    balance: 50000,
    created_at: new Date()
  }
])

// Create flags collection with your custom flag
db.flags.insertMany([
  {
    name: 'ctf_flag',
    value: 'WOLF{your_custom_flag_here}',  // Replace with your flag
    description: 'SQL Injection Challenge Flag',
    created_at: new Date()
  },
  {
    name: 'decoy_flag_1',
    value: 'FAKE{n0t_th3_r34l_fl4g}',
    description: 'Decoy flag - not the real one!',
    created_at: new Date()
  }
])
```

### Method 3: Using MongoDB Compass (GUI)

1. **Download MongoDB Compass:** https://www.mongodb.com/products/compass
2. **Connect using your connection string:**
   ```
   mongodb+srv://tamils343435:<EMAIL>/
   ```
3. **Create database:** `ctf_bank`
4. **Create collections:** `users` and `flags`
5. **Insert documents manually through the GUI**

## 🔧 Flag Management Scripts

### Create a Flag Management Script
```javascript
// flag-manager.js
const mongoose = require('mongoose');
require('dotenv').config();

const MONGODB_URI = process.env.MONGODB_URI;

const flagSchema = new mongoose.Schema({
    name: String,
    value: String,
    description: String,
    created_at: { type: Date, default: Date.now }
});

const Flag = mongoose.model('Flag', flagSchema);

async function setFlag(flagName, flagValue, description = '') {
    try {
        await mongoose.connect(MONGODB_URI);
        
        const result = await Flag.findOneAndUpdate(
            { name: flagName },
            { 
                value: flagValue, 
                description: description,
                created_at: new Date()
            },
            { upsert: true, new: true }
        );
        
        console.log(`✅ Flag '${flagName}' set to: ${flagValue}`);
        return result;
    } catch (error) {
        console.error('❌ Error setting flag:', error);
    } finally {
        await mongoose.disconnect();
    }
}

async function getFlag(flagName) {
    try {
        await mongoose.connect(MONGODB_URI);
        const flag = await Flag.findOne({ name: flagName });
        
        if (flag) {
            console.log(`🚩 Flag '${flagName}': ${flag.value}`);
            return flag.value;
        } else {
            console.log(`❌ Flag '${flagName}' not found`);
            return null;
        }
    } catch (error) {
        console.error('❌ Error getting flag:', error);
    } finally {
        await mongoose.disconnect();
    }
}

// Command line usage
if (require.main === module) {
    const args = process.argv.slice(2);
    const command = args[0];
    
    if (command === 'set' && args.length >= 3) {
        setFlag(args[1], args[2], args[3] || '');
    } else if (command === 'get' && args.length >= 2) {
        getFlag(args[1]);
    } else {
        console.log('Usage:');
        console.log('  node flag-manager.js set <flag_name> <flag_value> [description]');
        console.log('  node flag-manager.js get <flag_name>');
        console.log('');
        console.log('Examples:');
        console.log('  node flag-manager.js set ctf_flag "WOLF{my_custom_flag}"');
        console.log('  node flag-manager.js get ctf_flag');
    }
}

module.exports = { setFlag, getFlag };
```

### Usage Examples:
```bash
# Set a new flag
node flag-manager.js set ctf_flag "WOLF{sql_1nj3ct10n_m4st3r}"

# Get current flag
node flag-manager.js get ctf_flag

# Set a decoy flag
node flag-manager.js set decoy_flag_1 "FAKE{wrong_flag}"
```

## 🔍 Verification Steps

### 1. Check Database Connection
```bash
# Test connection
node -e "
const mongoose = require('mongoose');
mongoose.connect(process.env.MONGODB_URI || 'your_connection_string')
  .then(() => console.log('✅ Connected to MongoDB'))
  .catch(err => console.error('❌ Connection failed:', err));
"
```

### 2. Verify Flag Storage
```bash
# Check if flag exists
node -e "
const mongoose = require('mongoose');
const flagSchema = new mongoose.Schema({name: String, value: String});
const Flag = mongoose.model('Flag', flagSchema);

mongoose.connect(process.env.MONGODB_URI)
  .then(() => Flag.findOne({name: 'ctf_flag'}))
  .then(flag => {
    if (flag) console.log('✅ Flag found:', flag.value);
    else console.log('❌ Flag not found');
    process.exit(0);
  });
"
```

### 3. Test Flag Retrieval via API
```bash
# Start your server and test
curl http://localhost:3000/api/flag
```

## 🛡️ Security Best Practices

### 1. Environment Variables
- Never hardcode flags in source code
- Use `.env` files for local development
- Use platform environment variables for production

### 2. Database Security
- Use strong passwords for MongoDB Atlas
- Enable IP whitelisting
- Use connection string with authentication

### 3. Flag Rotation
- Change flags regularly for ongoing CTFs
- Keep backup of previous flags
- Log flag access attempts

## 🔄 Flag Rotation Script
```javascript
// rotate-flag.js
const { setFlag } = require('./flag-manager');

function generateRandomFlag() {
    const adjectives = ['hidden', 'secret', 'encrypted', 'protected', 'secure'];
    const nouns = ['data', 'treasure', 'key', 'code', 'mystery'];
    const numbers = Math.floor(Math.random() * 9999);
    
    const adj = adjectives[Math.floor(Math.random() * adjectives.length)];
    const noun = nouns[Math.floor(Math.random() * nouns.length)];
    
    return `WOLF{${adj}_${noun}_${numbers}}`;
}

async function rotateFlag() {
    const newFlag = generateRandomFlag();
    await setFlag('ctf_flag', newFlag, 'Auto-generated flag');
    console.log(`🔄 Flag rotated to: ${newFlag}`);
}

if (require.main === module) {
    rotateFlag();
}
```

## 📊 Monitoring Flag Access

### Add Logging to Server
```javascript
// Add to server.js
app.post('/api/login', async (req, res) => {
    // ... existing code ...
    
    // Log flag access attempts
    if (username.toLowerCase().includes('union') && username.toLowerCase().includes('select')) {
        console.log(`🚨 Flag access attempt from IP: ${req.ip} at ${new Date()}`);
        console.log(`   Payload: ${username}`);
    }
    
    // ... rest of code ...
});
```

This comprehensive guide covers all aspects of flag storage and management for your MongoDB-based CTF challenge. The flag is now properly secured and can be easily managed through environment variables or the provided scripts.
