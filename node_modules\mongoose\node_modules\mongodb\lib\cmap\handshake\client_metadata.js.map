{"version": 3, "file": "client_metadata.js", "sourceRoot": "", "sources": ["../../../src/cmap/handshake/client_metadata.ts"], "names": [], "mappings": ";;;AAAA,yBAAyB;AACzB,mCAAmC;AAEnC,qCAAyC;AACzC,uCAAwD;AAGxD,8DAA8D;AAC9D,MAAM,mBAAmB,GAAG,OAAO,CAAC,uBAAuB,CAAC,CAAC,OAAO,CAAC;AAyCrE,gBAAgB;AAChB,MAAa,mBAAmB;IAI9B,YAAoB,OAAe;QAAf,YAAO,GAAP,OAAO,CAAQ;QAH3B,aAAQ,GAAG,IAAI,GAAG,EAAE,CAAC;QAC7B,uCAAuC;QAC/B,iBAAY,GAAG,CAAC,CAAC;IACa,CAAC;IAEvC,sEAAsE;IAC/D,cAAc,CAAC,GAAW,EAAE,KAAmC;QACpE,2FAA2F;QAC3F,+DAA+D;QAC/D,MAAM,cAAc,GAAG,WAAI,CAAC,SAAS,CAAC,IAAI,GAAG,EAAE,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC;QAEhF,IAAI,cAAc,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,EAAE;YACrD,OAAO,KAAK,CAAC;SACd;QAED,IAAI,CAAC,YAAY,IAAI,cAAc,CAAC;QAEpC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QAE9B,OAAO,IAAI,CAAC;IACd,CAAC;IAED,QAAQ;QACN,OAAO,WAAI,CAAC,WAAW,CAAC,WAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;YACrD,YAAY,EAAE,KAAK;YACnB,cAAc,EAAE,KAAK;YACrB,aAAa,EAAE,KAAK;YACpB,WAAW,EAAE,KAAK;SACnB,CAAmB,CAAC;IACvB,CAAC;CACF;AA/BD,kDA+BC;AAGD;;;;;;;GAOG;AACH,SAAgB,kBAAkB,CAAC,OAAkC;IACnE,MAAM,gBAAgB,GAAG,IAAI,mBAAmB,CAAC,GAAG,CAAC,CAAC;IAEtD,MAAM,EAAE,OAAO,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;IACjC,sCAAsC;IACtC,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;QACtB,MAAM,IAAI,GACR,MAAM,CAAC,UAAU,CAAC,OAAO,EAAE,MAAM,CAAC,IAAI,GAAG;YACvC,CAAC,CAAC,OAAO,CAAC,OAAO;YACjB,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QACrE,gBAAgB,CAAC,cAAc,CAAC,aAAa,EAAE,EAAE,IAAI,EAAE,CAAC,CAAC;KAC1D;IAED,MAAM,EAAE,IAAI,GAAG,EAAE,EAAE,OAAO,GAAG,EAAE,EAAE,QAAQ,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC,UAAU,CAAC;IAEtE,MAAM,UAAU,GAAG;QACjB,IAAI,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,UAAU,IAAI,EAAE,CAAC,CAAC,CAAC,QAAQ;QACnD,OAAO,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,mBAAmB,IAAI,OAAO,EAAE,CAAC,CAAC,CAAC,mBAAmB;KACxF,CAAC;IAEF,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,QAAQ,EAAE,UAAU,CAAC,EAAE;QAC1D,MAAM,IAAI,iCAAyB,CACjC,iFAAiF,CAClF,CAAC;KACH;IAED,IAAI,WAAW,GAAG,cAAc,EAAE,CAAC;IACnC,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;QACvB,WAAW,GAAG,GAAG,WAAW,IAAI,QAAQ,EAAE,CAAC;KAC5C;IAED,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,UAAU,EAAE,WAAW,CAAC,EAAE;QAC7D,MAAM,IAAI,iCAAyB,CACjC,yEAAyE,CAC1E,CAAC;KACH;IAED,sFAAsF;IACtF,MAAM,MAAM,GAAG,IAAI,GAAG,EAAE;SACrB,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC,QAAQ,CAAC;SAC7B,GAAG,CAAC,cAAc,EAAE,OAAO,CAAC,IAAI,CAAC;SACjC,GAAG,CAAC,SAAS,EAAE,EAAE,CAAC,OAAO,EAAE,CAAC;SAC5B,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;IAE1B,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE;QAClD,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,EAAE,EAAE;YAC/B,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YACnB,IAAI,MAAM,CAAC,IAAI,KAAK,CAAC;gBAAE,MAAM;YAC7B,IAAI,gBAAgB,CAAC,cAAc,CAAC,IAAI,EAAE,MAAM,CAAC;gBAAE,MAAM;SAC1D;KACF;IAED,MAAM,OAAO,GAAG,UAAU,EAAE,CAAC;IAC7B,IAAI,OAAO,IAAI,IAAI,EAAE;QACnB,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,KAAK,EAAE,OAAO,CAAC,EAAE;YACpD,KAAK,MAAM,GAAG,IAAI,OAAO,CAAC,IAAI,EAAE,EAAE;gBAChC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;gBACpB,IAAI,OAAO,CAAC,IAAI,KAAK,CAAC;oBAAE,MAAM;gBAC9B,IAAI,gBAAgB,CAAC,cAAc,CAAC,KAAK,EAAE,OAAO,CAAC;oBAAE,MAAM;aAC5D;SACF;KACF;IAED,OAAO,gBAAgB,CAAC,QAAQ,EAAE,CAAC;AACrC,CAAC;AAhED,gDAgEC;AAED;;;GAGG;AACH,SAAgB,UAAU;IACxB,MAAM,EACJ,iBAAiB,GAAG,EAAE,EACtB,sBAAsB,GAAG,EAAE,EAC3B,wBAAwB,GAAG,EAAE,EAC7B,SAAS,GAAG,EAAE,EACd,aAAa,GAAG,EAAE,EAClB,MAAM,GAAG,EAAE,EACX,+BAA+B,GAAG,EAAE,EACpC,UAAU,GAAG,EAAE,EACf,kBAAkB,GAAG,EAAE,EACvB,eAAe,GAAG,EAAE,EACpB,oBAAoB,GAAG,EAAE,EACzB,aAAa,GAAG,EAAE,EACnB,GAAG,OAAO,CAAC,GAAG,CAAC;IAEhB,MAAM,SAAS,GACb,iBAAiB,CAAC,UAAU,CAAC,aAAa,CAAC,IAAI,sBAAsB,CAAC,MAAM,GAAG,CAAC,CAAC;IACnF,MAAM,WAAW,GAAG,wBAAwB,CAAC,MAAM,GAAG,CAAC,CAAC;IACxD,MAAM,SAAS,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC;IACnE,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;IAEvC,wDAAwD;IACxD,MAAM,OAAO,GAAG,IAAI,GAAG,EAAE,CAAC;IAE1B,yEAAyE;IACzE,IAAI,YAAY,IAAI,CAAC,CAAC,WAAW,IAAI,SAAS,CAAC,EAAE;QAC/C,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE;YAC5B,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC;SACtC;QAED,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;QAC9B,OAAO,OAAO,CAAC;KAChB;IAED,IAAI,SAAS,IAAI,CAAC,CAAC,WAAW,IAAI,SAAS,IAAI,YAAY,CAAC,EAAE;QAC5D,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YACzB,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;SACnC;QAED,IACE,+BAA+B,CAAC,MAAM,GAAG,CAAC;YAC1C,MAAM,CAAC,SAAS,CAAC,CAAC,+BAA+B,CAAC,EAClD;YACA,OAAO,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,YAAK,CAAC,+BAA+B,CAAC,CAAC,CAAC;SACtE;QAED,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;QAClC,OAAO,OAAO,CAAC;KAChB;IAED,IAAI,WAAW,IAAI,CAAC,CAAC,SAAS,IAAI,SAAS,IAAI,YAAY,CAAC,EAAE;QAC5D,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;QAClC,OAAO,OAAO,CAAC;KAChB;IAED,IAAI,SAAS,IAAI,CAAC,CAAC,WAAW,IAAI,SAAS,IAAI,YAAY,CAAC,EAAE;QAC5D,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE;YAC9B,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,eAAe,CAAC,CAAC;SACxC;QAED,IAAI,kBAAkB,CAAC,MAAM,GAAG,CAAC,IAAI,MAAM,CAAC,SAAS,CAAC,CAAC,kBAAkB,CAAC,EAAE;YAC1E,OAAO,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,YAAK,CAAC,kBAAkB,CAAC,CAAC,CAAC;SACzD;QAED,IAAI,oBAAoB,CAAC,MAAM,GAAG,CAAC,IAAI,MAAM,CAAC,SAAS,CAAC,CAAC,oBAAoB,CAAC,EAAE;YAC9E,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,IAAI,YAAK,CAAC,oBAAoB,CAAC,CAAC,CAAC;SAC7D;QAED,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;QAChC,OAAO,OAAO,CAAC;KAChB;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AA1ED,gCA0EC;AAcD;;;;;;;GAOG;AACH,SAAS,cAAc;IACrB,IAAI,MAAM,IAAI,UAAU,EAAE;QACxB,MAAM,OAAO,GAAG,OAAO,IAAI,EAAE,OAAO,EAAE,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC,eAAe,CAAC;QAEhG,OAAO,SAAS,OAAO,KAAK,EAAE,CAAC,UAAU,EAAE,EAAE,CAAC;KAC/C;IAED,IAAI,KAAK,IAAI,UAAU,EAAE;QACvB,MAAM,OAAO,GAAG,OAAO,GAAG,EAAE,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC;QAElF,OAAO,QAAQ,OAAO,KAAK,EAAE,CAAC,UAAU,EAAE,EAAE,CAAC;KAC9C;IAED,OAAO,WAAW,OAAO,CAAC,OAAO,KAAK,EAAE,CAAC,UAAU,EAAE,EAAE,CAAC;AAC1D,CAAC"}