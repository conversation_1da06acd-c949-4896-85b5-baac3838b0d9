version: '3.8'

services:
  # CTF Application - Using MongoDB Atlas
  ctf-app:
    build: .
    container_name: ctf-sql-injection
    restart: unless-stopped
    environment:
      MONGODB_URI: mongodb+srv://tamils343435:<EMAIL>/ctf_bank?retryWrites=true&w=majority&appName=Cluster0
      PORT: 3000
      FLAG: ${FLAG:-YOUR_FLAG_HERE}
    ports:
      - "3000:3000"
    volumes:
      - ./logs:/app/logs
