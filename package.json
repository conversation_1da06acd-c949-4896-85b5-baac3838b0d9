{"name": "sql-injection-ctf", "version": "1.0.0", "description": "CTF Challenge: SQL Injection with MongoDB Integration", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "setup": "node setup.js", "quick-start": "node quick-start.js", "flag": "node flag-manager.js", "flag:set": "node flag-manager.js set", "flag:get": "node flag-manager.js get", "flag:list": "node flag-manager.js list", "flag:rotate": "node flag-manager.js rotate"}, "keywords": ["ctf", "sql-injection", "mongodb", "security", "challenge"], "author": "CTF Challenge Creator", "license": "MIT", "dependencies": {"bcrypt": "^5.1.0", "body-parser": "^1.20.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "mongodb": "^6.17.0", "mongoose": "^7.5.0"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=14.0.0"}}