# 🚀 Quick Start Guide - CTF SQL Injection Challenge

## 📋 Overview
This is a complete CTF challenge featuring SQL injection vulnerabilities with MongoDB backend. The flag is securely stored in the database and can be extracted through SQL injection techniques.

## ⚡ Quick Setup (5 minutes)

### 1. Install Dependencies
```bash
npm install
```

### 2. Set Your Flag
```bash
# Edit .env file and set your custom flag
FLAG=WOLF{your_custom_flag_here}
```

### 3. Setup Database
```bash
npm run setup
```

### 4. Start Challenge
```bash
npm start
```

### 5. Access Challenge
Open http://localhost:3000 in your browser

## 🎯 Challenge Details

- **ID:** prob2
- **Title:** SQL INJECTION
- **Points:** 50
- **Difficulty:** Impossible
- **Database:** MongoDB Atlas (pre-configured)
- **Flag Location:** Stored in `flags` collection

## 🔧 Flag Management

### Set a Custom Flag
```bash
npm run flag:set ctf_flag "WOLF{your_new_flag}"
```

### Get Current Flag
```bash
npm run flag:get ctf_flag
```

### List All Flags
```bash
npm run flag:list
```

### Rotate Flag (Generate Random)
```bash
npm run flag:rotate
```

## 🌐 Hosting Options

### Option 1: Heroku (Recommended)
```bash
# Install Heroku CLI first
npm install -g heroku

# Run deployment script
node deploy-heroku.js
```

### Option 2: Vercel
```bash
npm install -g vercel
vercel
# Set environment variables in Vercel dashboard
```

### Option 3: Railway
```bash
npm install -g @railway/cli
railway login
railway init
railway up
```

## 🔍 Solution Hints

1. **Basic Testing:** Try special characters in the login form
2. **Injection Point:** Username field is vulnerable
3. **UNION SELECT:** Use SQL injection syntax to access other collections
4. **Flag Location:** Look for the `flags` collection
5. **Example Payload:** `admin' UNION SELECT * FROM flags--`

## 📁 File Structure

```
├── index.html              # Frontend interface
├── server.js              # Backend server with vulnerabilities
├── package.json           # Dependencies and scripts
├── setup.js              # Database initialization
├── flag-manager.js       # Flag management utility
├── .env                  # Environment variables
├── Procfile             # Heroku deployment
├── Dockerfile           # Container configuration
├── docker-compose.yml   # Multi-service deployment
├── README.md           # Detailed documentation
├── DATABASE_SETUP.md   # Database setup guide
├── HOSTING_GUIDE.md    # Hosting platforms guide
└── SOLUTION.md         # Complete solution walkthrough
```

## 🛠️ Available Scripts

```bash
npm start           # Start the challenge server
npm run dev         # Start with auto-reload
npm run setup       # Initialize database
npm run flag        # Flag management help
npm run flag:set    # Set a flag value
npm run flag:get    # Get a flag value
npm run flag:list   # List all flags
npm run flag:rotate # Generate and set random flag
```

## 🔒 Security Features

### Intentional Vulnerabilities (For Educational Use):
- SQL/NoSQL injection in login form
- Direct user input in database queries
- Information disclosure in responses
- Authentication bypass possibilities

### Security Measures:
- Environment variable based flag storage
- MongoDB Atlas secure connection
- No hardcoded secrets in source code
- Proper error handling

## 🎮 How Players Solve It

1. **Access the banking portal** at your hosted URL
2. **Test the login form** with various inputs
3. **Identify SQL injection** vulnerability in username field
4. **Use UNION SELECT** statements to extract data
5. **Target the flags collection** to find the real flag
6. **Submit the flag** in the submission form

## 🚨 Troubleshooting

### Common Issues:

**Database Connection Failed:**
- Check MongoDB Atlas connection string
- Verify network connectivity
- Ensure IP whitelist includes your hosting platform

**Flag Not Found:**
- Run `npm run setup` to initialize database
- Check if FLAG environment variable is set
- Use `npm run flag:list` to verify flag storage

**Port Already in Use:**
- Change PORT in .env file
- Kill existing processes: `pkill -f node`

**Deployment Failed:**
- Ensure all environment variables are set
- Check platform-specific requirements
- Verify git repository is initialized

## 📊 Expected Solve Statistics

- **Beginner CTF Players:** 45-60 minutes
- **Intermediate Players:** 20-30 minutes  
- **Advanced Players:** 10-15 minutes
- **Expert Players:** 5-10 minutes

## 🎯 Success Metrics

Players successfully complete the challenge when they:
1. Identify the SQL injection vulnerability
2. Craft working UNION SELECT payloads
3. Extract the flag from the database
4. Submit the correct flag value

## 💡 Tips for CTF Organizers

1. **Monitor Attempts:** Check server logs for injection attempts
2. **Provide Hints:** Use progressive hint system if players get stuck
3. **Flag Rotation:** Change flags between CTF rounds
4. **Scaling:** Use appropriate hosting tier for participant count
5. **Backup:** Keep database backups and flag records

## 🤝 Support

For issues or questions:
1. Check the troubleshooting section
2. Review the detailed documentation files
3. Test locally before deploying to production
4. Monitor server logs for error messages

---

**Happy Hacking! 🎯**

Your CTF SQL injection challenge is now ready to deploy and challenge participants!
