const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const bodyParser = require('body-parser');
const path = require('path');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(bodyParser.json());
app.use(express.static('.'));

// MongoDB connection
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb+srv://tamils343435:<EMAIL>/ctf_bank?retryWrites=true&w=majority&appName=Cluster0';

mongoose.connect(MONGODB_URI, {
    useNewUrlParser: true,
    useUnifiedTopology: true,
})
.then(() => {
    console.log('✅ Connected to MongoDB');
    initializeDatabase();
})
.catch(err => {
    console.error('❌ MongoDB connection error:', err);
    console.log('💡 Make sure MongoDB is running or provide a valid MONGODB_URI in .env file');
});

// User Schema
const userSchema = new mongoose.Schema({
    username: String,
    password: String,
    email: String,
    account_type: String,
    balance: Number,
    created_at: { type: Date, default: Date.now }
});

// Flag Schema (separate collection for the flag)
const flagSchema = new mongoose.Schema({
    name: String,
    value: String,
    description: String,
    created_at: { type: Date, default: Date.now }
});

const User = mongoose.model('User', userSchema);
const Flag = mongoose.model('Flag', flagSchema);

// Initialize database with sample data
async function initializeDatabase() {
    try {
        // Clear existing data
        await User.deleteMany({});
        await Flag.deleteMany({});

        // Create sample users
        const users = [
            {
                username: 'admin',
                password: 'admin123',
                email: '<EMAIL>',
                account_type: 'administrator',
                balance: 1000000
            },
            {
                username: 'john_doe',
                password: 'password123',
                email: '<EMAIL>',
                account_type: 'premium',
                balance: 50000
            },
            {
                username: 'jane_smith',
                password: 'mypassword',
                email: '<EMAIL>',
                account_type: 'standard',
                balance: 25000
            },
            {
                username: 'guest',
                password: 'guest',
                email: '<EMAIL>',
                account_type: 'guest',
                balance: 0
            }
        ];

        await User.insertMany(users);

        // Create flag entry - flag will be loaded from database
        const flag = new Flag({
            name: 'ctf_flag',
            value: process.env.FLAG || 'FLAG_NOT_SET',
            description: 'SQL Injection Challenge Flag'
        });

        await flag.save();

        console.log('✅ Database initialized with sample data');
        console.log('👤 Sample users: admin, john_doe, jane_smith, guest');
        console.log('🚩 Flag stored in flags collection');
    } catch (error) {
        console.error('❌ Error initializing database:', error);
    }
}

// Serve the main page
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'index.html'));
});

// Vulnerable login endpoint - INTENTIONALLY VULNERABLE FOR CTF
app.post('/api/login', async (req, res) => {
    try {
        const { username, password } = req.body;

        if (!username || !password) {
            return res.json({
                success: false,
                message: 'Username and password are required'
            });
        }

        // VULNERABLE: Direct string concatenation for MongoDB query
        // This simulates SQL injection in a NoSQL context
        let query;
        let result;

        // Check if this looks like a UNION SELECT injection attempt
        if (username.toLowerCase().includes('union') && username.toLowerCase().includes('select')) {
            // Simulate SQL injection behavior in MongoDB context
            try {
                // Extract the UNION SELECT part and try to get flag data
                if (username.toLowerCase().includes('flags') || username.toLowerCase().includes('flag')) {
                    const flag = await Flag.findOne({ name: 'ctf_flag' });
                    const users = await User.find({}, { password: 0 }); // Exclude passwords
                    
                    return res.json({
                        success: true,
                        message: 'UNION SELECT injection successful!',
                        query: `db.users.find({username: "${username}", password: "${password}"})`,
                        flag: flag ? flag.value : null,
                        users: users,
                        user: {
                            username: 'injected_user',
                            account_type: 'hacker'
                        }
                    });
                }
            } catch (injectionError) {
                return res.json({
                    success: false,
                    message: 'Injection attempt detected but failed',
                    query: `db.users.find({username: "${username}", password: "${password}"})`
                });
            }
        }

        // Normal authentication (also vulnerable to NoSQL injection)
        // VULNERABLE: Using user input directly in query
        query = { username: username, password: password };
        result = await User.findOne(query);

        if (result) {
            // Remove password from response
            const userResponse = {
                username: result.username,
                email: result.email,
                account_type: result.account_type,
                balance: result.balance
            };

            res.json({
                success: true,
                message: 'Login successful',
                user: userResponse,
                query: `db.users.find({username: "${username}", password: "${password}"})`
            });
        } else {
            res.json({
                success: false,
                message: 'Invalid username or password',
                query: `db.users.find({username: "${username}", password: "${password}"})`
            });
        }

    } catch (error) {
        console.error('Login error:', error);
        res.json({
            success: false,
            message: 'Server error occurred',
            error: error.message
        });
    }
});

// API endpoint to get users (for testing)
app.get('/api/users', async (req, res) => {
    try {
        const users = await User.find({}, { password: 0 }); // Exclude passwords
        res.json({ users });
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// API endpoint to get flag (protected)
app.get('/api/flag', async (req, res) => {
    try {
        const flag = await Flag.findOne({ name: 'ctf_flag' });
        res.json({ flag: flag ? flag.value : 'Flag not found' });
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// API endpoint to check submitted flag
app.post('/api/check-flag', async (req, res) => {
    try {
        const { flag } = req.body;
        const correctFlag = await Flag.findOne({ name: 'ctf_flag' });

        if (!correctFlag) {
            return res.json({ correct: false, message: 'Flag not found in database' });
        }

        const isCorrect = flag && flag.trim() === correctFlag.value;
        res.json({
            correct: isCorrect,
            message: isCorrect ? 'Correct flag!' : 'Incorrect flag'
        });
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// Health check endpoint
app.get('/api/health', (req, res) => {
    res.json({ 
        status: 'OK', 
        message: 'CTF SQL Injection Challenge Server',
        mongodb: mongoose.connection.readyState === 1 ? 'Connected' : 'Disconnected'
    });
});

app.listen(PORT, () => {
    console.log(`🚀 CTF Server running on http://localhost:${PORT}`);
    console.log(`📊 Health check: http://localhost:${PORT}/api/health`);
    console.log(`🎯 Challenge: http://localhost:${PORT}`);
    console.log('');
    console.log('💡 Challenge Hints:');
    console.log('   - Try SQL injection techniques in the login form');
    console.log('   - Use UNION SELECT to extract data from other collections');
    console.log('   - Look for the flag in the flags collection');
    console.log('   - Example payload: admin\' UNION SELECT * FROM flags--');
});

module.exports = app;
