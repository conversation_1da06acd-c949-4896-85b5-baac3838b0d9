# CTF Challenge Solution: SQL Injection with MongoDB

## 🎯 Challenge Overview
- **ID:** prob2
- **Title:** SQL INJECTION
- **Points:** 50
- **Difficulty:** Impossible
- **Flag:** Set via environment variable (check .env file)

## 🔍 Vulnerability Analysis

### Vulnerable Code Location
The vulnerability exists in `server.js` at the `/api/login` endpoint:

```javascript
// VULNERABLE: Direct string concatenation for MongoDB query
let query = { username: username, password: password };
result = await User.findOne(query);
```

### Vulnerability Type
- **Primary:** NoSQL Injection (MongoDB)
- **Secondary:** Information Disclosure
- **Impact:** Authentication Bypass, Data Extraction

## 🛠️ Solution Steps

### Step 1: Initial Reconnaissance
1. Access the challenge at `http://localhost:3000`
2. Analyze the login form for injection points
3. Test basic authentication with known credentials

### Step 2: Identify Injection Point
1. Try special characters in the username field:
   - `admin'`
   - `admin"`
   - `admin\`
2. Observe error messages or different responses

### Step 3: Test Basic Injection
1. Try simple NoSQL injection payloads:
   - `{"$ne": null}`
   - `admin' || '1'=='1`
2. Look for authentication bypass

### Step 4: UNION SELECT Exploitation
The key to this challenge is using SQL-style UNION SELECT syntax, which the vulnerable server interprets specially:

**Successful Payload:**
```
Username: admin' UNION SELECT * FROM flags--
Password: anything
```

**Alternative Payloads:**
```
admin' UNION SELECT value FROM flags WHERE name='ctf_flag'--
admin' UNION SELECT * FROM flags WHERE name LIKE '%ctf%'--
' UNION SELECT * FROM flags--
```

### Step 5: Extract the Flag
When the UNION SELECT payload is successful, the server returns:
- User authentication data
- **Flag value:** The flag set in your environment variable
- Additional database information

### Step 6: Submit Flag
Enter the discovered flag in the flag submission form.

## 🔧 Technical Details

### Server Response Analysis
The vulnerable server processes UNION SELECT payloads by:
1. Detecting keywords "union" and "select" in the username
2. Checking for "flags" or "flag" keywords
3. Executing a MongoDB query to fetch flag data
4. Returning the flag in the JSON response

### MongoDB Collections
- **users:** Contains user authentication data
- **flags:** Contains the challenge flag and decoy flags
  - `ctf_flag`: Real flag (set via environment)
  - `decoy_flag_1`: Fake flag
  - `decoy_flag_2`: Another fake flag

## 🎓 Learning Outcomes

### For Participants
1. **NoSQL Injection:** Understanding how NoSQL databases can be vulnerable
2. **UNION SELECT:** Learning SQL injection techniques
3. **Authentication Bypass:** Exploiting login mechanisms
4. **Data Extraction:** Retrieving sensitive information

### Security Lessons
1. **Input Validation:** Always validate and sanitize user input
2. **Parameterized Queries:** Use proper query parameterization
3. **Least Privilege:** Limit database access permissions
4. **Error Handling:** Don't expose sensitive information in errors

## 🚨 Common Mistakes

### Participant Mistakes
1. **Wrong Collection:** Looking in the wrong database collection
2. **Syntax Errors:** Incorrect UNION SELECT syntax
3. **Case Sensitivity:** Not considering case-sensitive keywords
4. **Decoy Flags:** Submitting decoy flags instead of the real one

### Setup Mistakes
1. **MongoDB Not Running:** Database connection failures
2. **Wrong Environment:** Incorrect MongoDB URI configuration
3. **Port Conflicts:** Server port already in use
4. **Dependencies:** Missing Node.js packages
5. **Flag Not Set:** Forgetting to set the FLAG environment variable

## 🔄 Alternative Solutions

### Method 1: Direct NoSQL Injection
```javascript
// Payload in username field
{"$ne": null}
```

### Method 2: JavaScript Injection
```javascript
// Payload in username field
admin'; return true; //
```

### Method 3: API Endpoint Discovery
```bash
# Direct API access (if discovered)
curl http://localhost:3000/api/flag
```

## 🛡️ Mitigation Strategies

### Immediate Fixes
1. **Input Sanitization:**
   ```javascript
   const sanitizedUsername = username.replace(/['"\\]/g, '');
   ```

2. **Parameterized Queries:**
   ```javascript
   const query = { username: { $eq: username }, password: { $eq: password } };
   ```

3. **Input Validation:**
   ```javascript
   if (!/^[a-zA-Z0-9_]+$/.test(username)) {
       return res.status(400).json({ error: 'Invalid username format' });
   }
   ```

### Long-term Security
1. **Use ORM/ODM:** Implement Mongoose with proper schemas
2. **Authentication Libraries:** Use passport.js or similar
3. **Rate Limiting:** Implement login attempt limits
4. **Logging:** Monitor and log injection attempts

## 📊 Challenge Statistics

### Expected Solve Time
- **Beginner:** 45-60 minutes
- **Intermediate:** 20-30 minutes
- **Advanced:** 10-15 minutes

### Common Solve Path
1. Initial testing: 5-10 minutes
2. Injection discovery: 10-15 minutes
3. UNION SELECT exploitation: 15-20 minutes
4. Flag extraction: 5 minutes

## 🎯 Challenge Variations

### Difficulty Adjustments
- **Easier:** Provide more obvious error messages
- **Harder:** Add WAF simulation, require blind injection
- **Expert:** Implement time-based injection requirements

### Extension Ideas
1. **Multi-step:** Require multiple injection points
2. **Blind Injection:** Remove direct flag disclosure
3. **WAF Bypass:** Add input filtering to bypass

---

**Note:** The actual flag value is set via the FLAG environment variable in your .env file.
