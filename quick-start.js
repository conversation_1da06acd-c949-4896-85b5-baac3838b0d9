#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 CTF SQL Injection Challenge - Quick Start Setup');
console.log('==================================================');

// Check if .env exists
if (!fs.existsSync('.env')) {
    console.log('📝 Creating .env file...');
    
    // Prompt for flag
    const readline = require('readline');
    const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout
    });
    
    rl.question('Enter your custom flag (or press Enter for default): ', (flag) => {
        const flagValue = flag.trim() || 'WOLF{un10n_s3l3ct_m0ng0_1nj3ct10n}';
        
        const envContent = `# MongoDB Configuration
MONGODB_URI=mongodb+srv://tamils343435:<EMAIL>/ctf_bank?retryWrites=true&w=majority&appName=Cluster0

# Server Configuration
PORT=3000

# Challenge Configuration
FLAG=${flagValue}
`;
        
        fs.writeFileSync('.env', envContent);
        console.log('✅ .env file created with your flag');
        
        rl.close();
        
        // Continue with setup
        setupDatabase();
    });
} else {
    console.log('✅ .env file already exists');
    setupDatabase();
}

function setupDatabase() {
    console.log('🔄 Setting up database...');
    
    try {
        execSync('npm run setup', { stdio: 'inherit' });
        console.log('✅ Database setup complete!');
        
        console.log('\n🎯 Challenge is ready!');
        console.log('Run "npm start" to start the server');
        console.log('Then visit http://localhost:3000');
        
    } catch (error) {
        console.error('❌ Database setup failed:', error.message);
        console.log('💡 Make sure you have internet connection for MongoDB Atlas');
        process.exit(1);
    }
}
