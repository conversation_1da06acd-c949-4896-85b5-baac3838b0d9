const mongoose = require('mongoose');
require('dotenv').config();

// MongoDB connection
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb+srv://tamils343435:<EMAIL>/ctf_bank?retryWrites=true&w=majority&appName=Cluster0';

// Schemas
const userSchema = new mongoose.Schema({
    username: String,
    password: String,
    email: String,
    account_type: String,
    balance: Number,
    created_at: { type: Date, default: Date.now }
});

const flagSchema = new mongoose.Schema({
    name: String,
    value: String,
    description: String,
    created_at: { type: Date, default: Date.now }
});

const User = mongoose.model('User', userSchema);
const Flag = mongoose.model('Flag', flagSchema);

async function setupDatabase() {
    try {
        console.log('🔄 Connecting to MongoDB...');
        await mongoose.connect(MONGODB_URI, {
            useNewUrlParser: true,
            useUnifiedTopology: true,
        });
        console.log('✅ Connected to MongoDB');

        // Clear existing data
        console.log('🧹 Clearing existing data...');
        await User.deleteMany({});
        await Flag.deleteMany({});

        // Create sample users
        console.log('👤 Creating sample users...');
        const users = [
            {
                username: 'admin',
                password: 'admin123',
                email: '<EMAIL>',
                account_type: 'administrator',
                balance: 1000000
            },
            {
                username: 'john_doe',
                password: 'password123',
                email: '<EMAIL>',
                account_type: 'premium',
                balance: 50000
            },
            {
                username: 'jane_smith',
                password: 'mypassword',
                email: '<EMAIL>',
                account_type: 'standard',
                balance: 25000
            },
            {
                username: 'guest',
                password: 'guest',
                email: '<EMAIL>',
                account_type: 'guest',
                balance: 0
            },
            {
                username: 'test_user',
                password: 'test123',
                email: '<EMAIL>',
                account_type: 'standard',
                balance: 10000
            }
        ];

        await User.insertMany(users);
        console.log(`✅ Created ${users.length} sample users`);

        // Create flag entry
        console.log('🚩 Creating flag entry...');
        const flagValue = process.env.FLAG;
        if (!flagValue) {
            console.error('❌ FLAG environment variable not set!');
            console.log('💡 Please set the FLAG environment variable before running setup');
            process.exit(1);
        }

        const flag = new Flag({
            name: 'ctf_flag',
            value: flagValue,
            description: 'SQL Injection Challenge Flag - Find me using UNION SELECT!'
        });

        await flag.save();
        console.log('✅ Flag created and stored securely');

        // Create additional decoy flags
        const decoyFlags = [
            {
                name: 'decoy_flag_1',
                value: 'FAKE{n0t_th3_r34l_fl4g}',
                description: 'Decoy flag - not the real one!'
            },
            {
                name: 'decoy_flag_2',
                value: 'FAKE{k33p_l00k1ng}',
                description: 'Another decoy flag'
            }
        ];

        await Flag.insertMany(decoyFlags);
        console.log('✅ Created decoy flags for added challenge');

        console.log('\n🎯 Database Setup Complete!');
        console.log('📊 Summary:');
        console.log(`   - Users created: ${users.length}`);
        console.log(`   - Flags created: ${decoyFlags.length + 1}`);
        console.log(`   - Database: ${MONGODB_URI}`);
        console.log('\n💡 Challenge Info:');
        console.log('   - Real flag is stored in the "flags" collection');
        console.log('   - Use SQL injection techniques to extract it');
        console.log('   - Try UNION SELECT statements in the login form');
        console.log('\n🚀 Ready to start the challenge!');

    } catch (error) {
        console.error('❌ Setup failed:', error);
    } finally {
        await mongoose.disconnect();
        console.log('🔌 Disconnected from MongoDB');
        process.exit(0);
    }
}

// Run setup if this file is executed directly
if (require.main === module) {
    setupDatabase();
}

module.exports = { setupDatabase };
