// MongoDB initialization script for Docker
db = db.getSiblingDB('ctf_bank');

// Create users collection with sample data
db.users.insertMany([
    {
        username: 'admin',
        password: 'admin123',
        email: '<EMAIL>',
        account_type: 'administrator',
        balance: 1000000,
        created_at: new Date()
    },
    {
        username: 'john_doe',
        password: 'password123',
        email: '<EMAIL>',
        account_type: 'premium',
        balance: 50000,
        created_at: new Date()
    },
    {
        username: 'jane_smith',
        password: 'mypassword',
        email: '<EMAIL>',
        account_type: 'standard',
        balance: 25000,
        created_at: new Date()
    },
    {
        username: 'guest',
        password: 'guest',
        email: '<EMAIL>',
        account_type: 'guest',
        balance: 0,
        created_at: new Date()
    }
]);

// Create flags collection with decoy flags only
// Real flag will be set via environment variable during setup
db.flags.insertMany([
    {
        name: 'decoy_flag_1',
        value: 'FAKE{n0t_th3_r34l_fl4g}',
        description: 'Decoy flag - not the real one!',
        created_at: new Date()
    },
    {
        name: 'decoy_flag_2',
        value: 'FAKE{k33p_l00k1ng}',
        description: 'Another decoy flag',
        created_at: new Date()
    }
]);

print('CTF Database initialized successfully!');
print('Users created: ' + db.users.count());
print('Flags created: ' + db.flags.count());
