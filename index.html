<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CTF - SQL Injection Challenge</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            background-color: #000000;
            color: #c9d1d9;
            margin: 0;
            padding: 20px;
            line-height: 1.6;
            user-select: none;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background-color: #000000;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #ffffff;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .title {
            color: #f85149;
            font-size: 2.5em;
            margin: 0;
            text-shadow: 0 0 10px rgba(248, 81, 73, 0.3);
        }
        .difficulty {
            background-color: #dc2626;
            color: #fff;
            padding: 5px 15px;
            border-radius: 20px;
            font-weight: bold;
            display: inline-block;
            margin-top: 10px;
        }
        .points {
            color: #7c3aed;
            font-size: 1.2em;
            font-weight: bold;
        }
        .description {
            background-color: #1e293b;
            padding: 20px;
            border-radius: 6px;
            border-left: 4px solid #58a6ff;
            margin: 20px 0;
            color: #e2e8f0;
        }
        .challenge-section {
            background-color: #1e293b;
            border: 1px solid #334155;
            border-radius: 6px;
            padding: 20px;
            margin: 20px 0;
        }
        .login-form {
            background-color: #0f172a;
            padding: 20px;
            border-radius: 6px;
            border: 1px solid #475569;
            margin: 20px 0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #cbd5e1;
        }
        .form-input {
            width: 100%;
            padding: 10px;
            background-color: #1e293b;
            border: 1px solid #475569;
            border-radius: 4px;
            color: #e2e8f0;
            font-family: 'Courier New', monospace;
            box-sizing: border-box;
        }
        .submit-btn {
            background-color: #10b981;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-family: inherit;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        .submit-btn:hover {
            background-color: #059669;
        }
        .hint-section {
            background-color: #7c2d12;
            border: 1px solid #dc2626;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
            color: #fecaca;
        }
        .result-section {
            background-color: #1f2937;
            border: 1px solid #374151;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
            min-height: 50px;
        }
        .flag-section {
            background-color: #1e293b;
            border: 1px solid #334155;
            border-radius: 6px;
            padding: 20px;
            margin: 20px 0;
        }
        .flag-input {
            width: 70%;
            padding: 10px;
            background-color: #0f172a;
            border: 1px solid #475569;
            border-radius: 4px;
            color: #e2e8f0;
            font-family: 'Courier New', monospace;
        }
        .protected-text {
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            -khtml-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }
        .code-block {
            background-color: #0f172a;
            border: 1px solid #374151;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            color: #10b981;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">SQL INJECTION</h1>
            <div class="points">Points: 50</div>
            <div class="difficulty">Impossible</div>
        </div>

        <div class="description">
            <h3>🎯 Challenge Description</h3>
            <p class="protected-text">
                Welcome to the SecureBank login system! This banking application uses a robust authentication system 
                to protect user accounts. Your mission is to exploit a SQL injection vulnerability to extract sensitive 
                data from the database and find the hidden flag.
            </p>
            <p class="protected-text">
                The application connects to a MongoDB database that stores user credentials and sensitive information. 
                Can you bypass the authentication and retrieve the flag?
            </p>
        </div>

        <div class="hint-section">
            <h3>💡 Hint</h3>
            <p class="protected-text">Try using UNION SELECT statements to extract data from other tables. 
            The flag might be stored in a different collection than the user data.</p>
        </div>

        <div class="challenge-section">
            <h3>🏦 SecureBank Login Portal</h3>
            <div class="login-form">
                <form id="loginForm">
                    <div class="form-group">
                        <label for="username">Username:</label>
                        <input type="text" id="username" name="username" class="form-input" 
                               placeholder="Enter your username" required>
                    </div>
                    <div class="form-group">
                        <label for="password">Password:</label>
                        <input type="password" id="password" name="password" class="form-input" 
                               placeholder="Enter your password" required>
                    </div>
                    <button type="submit" class="submit-btn">🔐 Login</button>
                </form>
            </div>
            
            <div id="loginResult" class="result-section">
                <p>Enter your credentials to access the banking system...</p>
            </div>
        </div>

        <div class="flag-section">
            <h3>🚩 Submit Flag</h3>
            <p class="protected-text">Found the flag? Submit it here:</p>
            <input type="text" id="flagInput" class="flag-input" placeholder="WOLF{...}">
            <button onclick="checkFlag()" class="submit-btn">Submit Flag</button>
            <div id="flagResult" style="margin-top: 10px;"></div>
        </div>
    </div>

    <script>
        // Disable right-click and developer tools
        document.addEventListener('contextmenu', function(e) {
            e.preventDefault();
            showSecurityAlert();
            return false;
        });

        document.addEventListener('keydown', function(e) {
            if (e.keyCode === 123 || (e.ctrlKey && e.shiftKey && e.keyCode === 73) || 
                (e.ctrlKey && e.keyCode === 85) || (e.ctrlKey && e.keyCode === 83)) {
                e.preventDefault();
                showSecurityAlert();
                return false;
            }
        });

        function showSecurityAlert() {
            const alertDiv = document.createElement('div');
            alertDiv.style.cssText = `
                position: fixed; top: 20px; right: 20px; background-color: #dc2626;
                color: white; padding: 10px 20px; border-radius: 6px; z-index: 9999;
                font-family: 'Courier New', monospace;
            `;
            alertDiv.textContent = '🔒 Action blocked for security';
            document.body.appendChild(alertDiv);
            setTimeout(() => document.body.removeChild(alertDiv), 2000);
        }

        // Handle login form submission
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('loginResult');
            
            resultDiv.innerHTML = '<p>🔄 Authenticating...</p>';
            
            try {
                const response = await fetch('/api/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, password })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div style="color: #10b981;">
                            <h4>✅ Login Successful!</h4>
                            <p>Welcome, ${data.user.username}!</p>
                            <p>Account Type: ${data.user.account_type}</p>
                            ${data.flag ? `<div class="code-block">🚩 FLAG: ${data.flag}</div>` : ''}
                            ${data.users ? `<div class="code-block">👥 Users: ${JSON.stringify(data.users, null, 2)}</div>` : ''}
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div style="color: #f85149;">
                            <h4>❌ Login Failed</h4>
                            <p>${data.message}</p>
                            ${data.query ? `<div class="code-block">Query: ${data.query}</div>` : ''}
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div style="color: #f85149;">
                        <h4>❌ Connection Error</h4>
                        <p>Unable to connect to the server. Make sure the backend is running.</p>
                    </div>
                `;
            }
        });

        async function checkFlag() {
            const input = document.getElementById('flagInput').value.trim();
            const result = document.getElementById('flagResult');

            try {
                const response = await fetch('/api/check-flag', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ flag: input })
                });

                const data = await response.json();

                if (data.correct) {
                    result.innerHTML = '<div style="color: #10b981; font-weight: bold;">🎉 Correct! Well done!</div>';
                } else {
                    result.innerHTML = '<div style="color: #f85149; font-weight: bold;">❌ Incorrect flag. Try again!</div>';
                }
            } catch (error) {
                result.innerHTML = '<div style="color: #f85149; font-weight: bold;">❌ Error checking flag. Try again!</div>';
            }
        }
    </script>
</body>
</html>
