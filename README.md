# CTF Challenge: SQL Injection with MongoDB

**Challenge ID:** prob2  
**Title:** SQL INJECTION  
**Description:** Exploit the SQL injection vulnerability to extract data.  
**Points:** 50  
**Difficulty:** Impossible  
**Hint:** Try using UNION SELECT statements  

## 🎯 Challenge Overview

Welcome to the SecureBank SQL Injection Challenge! This CTF problem simulates a vulnerable banking application that uses MongoDB for data storage. Your mission is to exploit SQL injection vulnerabilities to bypass authentication and extract the hidden flag from the database.

## 🏗️ Architecture

- **Frontend:** HTML/CSS/JavaScript interface mimicking a banking login portal
- **Backend:** Node.js/Express server with intentionally vulnerable endpoints
- **Database:** MongoDB with user accounts and flag storage
- **Flag Storage:** Separate MongoDB collection for secure flag storage

## 🚀 Quick Start

### Prerequisites

- Node.js (v14 or higher)
- MongoDB (local installation or MongoDB Atlas)
- npm or yarn package manager

### Installation

1. **<PERSON>lone and navigate to the challenge directory:**
   ```bash
   cd ctf_problems/problem-2
   ```

2. **Install dependencies:**
   ```bash
   npm install
   ```

3. **Configure environment:**
   ```bash
   cp .env.example .env
   # Edit .env file and set your FLAG
   # MongoDB Atlas connection is pre-configured
   ```

4. **Set your flag:**
   ```bash
   # Edit .env file and replace YOUR_FLAG_HERE with your actual flag
   FLAG=WOLF{your_custom_flag_here}
   ```

5. **Setup database:**
   ```bash
   npm run setup
   ```

6. **Start the challenge server:**
   ```bash
   npm start
   ```

7. **Access the challenge:**
   Open your browser and navigate to `http://localhost:3000`

## 🔧 Configuration

### MongoDB Setup Options

#### Option 1: Local MongoDB
```bash
# Install MongoDB locally
# Ubuntu/Debian:
sudo apt-get install mongodb

# macOS:
brew install mongodb-community

# Start MongoDB service
sudo systemctl start mongodb  # Linux
brew services start mongodb-community  # macOS
```

#### Option 2: MongoDB Atlas (Cloud)
1. Create a free account at [MongoDB Atlas](https://www.mongodb.com/atlas)
2. Create a new cluster
3. Get your connection string
4. Update `.env` file with your Atlas connection string

### Environment Variables

```env
# MongoDB Atlas (pre-configured)
MONGODB_URI=mongodb+srv://tamils343435:<EMAIL>/ctf_bank?retryWrites=true&w=majority&appName=Cluster0

# Server Configuration
PORT=3000

# Challenge Configuration - SET YOUR CUSTOM FLAG
FLAG=YOUR_FLAG_HERE
```

## 🎮 How to Play

1. **Access the Challenge:** Navigate to `http://localhost:3000`
2. **Analyze the Login Form:** Look for potential injection points
3. **Test Basic Payloads:** Try simple SQL injection techniques
4. **Exploit UNION SELECT:** Use UNION SELECT to extract data from other collections
5. **Find the Flag:** The flag is stored in the `flags` collection
6. **Submit the Flag:** Enter the discovered flag in the submission form

## 🔍 Challenge Details

### Sample Users
The database contains several test accounts:
- `admin` / `admin123` (administrator)
- `john_doe` / `password123` (premium)
- `jane_smith` / `mypassword` (standard)
- `guest` / `guest` (guest)

### Vulnerability Points
- Login form accepts user input without proper sanitization
- Direct string concatenation in MongoDB queries
- UNION SELECT simulation for NoSQL injection
- Flag stored in separate collection accessible via injection

### Expected Solution Path
1. Identify SQL injection vulnerability in login form
2. Test basic injection payloads
3. Use UNION SELECT to access the `flags` collection
4. Extract the flag value: `WOLF{un10n_s3l3ct_m0ng0_1nj3ct10n}`

## 🛠️ Development Commands

```bash
# Start development server with auto-reload
npm run dev

# Setup/reset database
npm run setup

# Start production server
npm start
```

## 🔒 Security Notes

**⚠️ WARNING:** This application contains intentional security vulnerabilities for educational purposes. DO NOT use this code in production environments.

### Intentional Vulnerabilities:
- SQL/NoSQL injection in authentication
- Direct user input in database queries
- Exposed database query information
- Unvalidated user input

## 🏆 Solution Hints

<details>
<summary>Click to reveal progressive hints</summary>

### Hint 1: Basic Testing
Try entering special characters like `'`, `"`, or `\` in the login form to see how the application responds.

### Hint 2: Injection Point
The username field is vulnerable to injection. Try payloads that include SQL keywords.

### Hint 3: UNION SELECT
Use UNION SELECT syntax to extract data from other collections. The flag is in the `flags` collection.

### Hint 4: Example Payload
Try something like: `admin' UNION SELECT * FROM flags--`

### Hint 5: Flag Location
The real flag has the name `ctf_flag` in the flags collection.

</details>

## 🐛 Troubleshooting

### Common Issues

1. **MongoDB Connection Failed**
   - Ensure MongoDB is running locally or check Atlas connection string
   - Verify network connectivity and credentials

2. **Port Already in Use**
   - Change the PORT in `.env` file
   - Kill existing processes using the port

3. **Database Not Initialized**
   - Run `npm run setup` to initialize the database
   - Check MongoDB logs for errors

4. **Frontend Not Loading**
   - Ensure server is running on correct port
   - Check browser console for JavaScript errors

## 📚 Learning Objectives

After completing this challenge, participants will understand:
- SQL injection attack vectors
- NoSQL injection techniques
- UNION SELECT statement usage
- Database security best practices
- Input validation importance

## 🤝 Contributing

This challenge is part of the Firebase CTF Forge project. For improvements or bug reports, please submit issues or pull requests to the main repository.

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

---

**Happy Hacking! 🎯**
