# CTF Challenge Hosting Guide

## 🌐 Platform Hosting Options

### 1. **Heroku** (Recommended for Beginners)
**Pros:** Easy deployment, free tier available, automatic scaling
**Cons:** Free tier has limitations, can sleep after 30 minutes

#### Setup Steps:
```bash
# Install Heroku CLI
npm install -g heroku

# Login to Heroku
heroku login

# Create Heroku app
heroku create your-ctf-challenge-name

# Set environment variables
heroku config:set MONGODB_URI="mongodb+srv://tamils343435:<EMAIL>/ctf_bank?retryWrites=true&w=majority&appName=Cluster0"
heroku config:set FLAG="WOLF{your_custom_flag_here}"
heroku config:set PORT=3000

# Deploy
git add .
git commit -m "Deploy CTF challenge"
git push heroku main

# Open your app
heroku open
```

#### Heroku Configuration Files:
Create `Procfile`:
```
web: node server.js
```

### 2. **Vercel** (Great for Node.js)
**Pros:** Fast deployment, excellent performance, free tier
**Cons:** Serverless limitations, cold starts

#### Setup Steps:
```bash
# Install Vercel CLI
npm install -g vercel

# Deploy
vercel

# Set environment variables in Vercel dashboard
# MONGODB_URI, FLAG, PORT
```

#### Vercel Configuration:
Create `vercel.json`:
```json
{
  "version": 2,
  "builds": [
    {
      "src": "server.js",
      "use": "@vercel/node"
    }
  ],
  "routes": [
    {
      "src": "/(.*)",
      "dest": "/server.js"
    }
  ],
  "env": {
    "MONGODB_URI": "@mongodb_uri",
    "FLAG": "@flag",
    "PORT": "3000"
  }
}
```

### 3. **Railway** (Modern Alternative)
**Pros:** Simple deployment, good free tier, automatic HTTPS
**Cons:** Newer platform, smaller community

#### Setup Steps:
```bash
# Install Railway CLI
npm install -g @railway/cli

# Login
railway login

# Initialize project
railway init

# Deploy
railway up

# Set environment variables
railway variables set MONGODB_URI="your_mongodb_uri"
railway variables set FLAG="your_flag"
```

### 4. **Render** (Reliable Option)
**Pros:** Free tier, automatic deployments, good documentation
**Cons:** Can be slower than other platforms

#### Setup Steps:
1. Connect your GitHub repository to Render
2. Create a new Web Service
3. Set environment variables in Render dashboard
4. Deploy automatically from GitHub

### 5. **DigitalOcean App Platform**
**Pros:** Reliable, good performance, competitive pricing
**Cons:** No free tier, requires payment

#### Setup Steps:
1. Create DigitalOcean account
2. Use App Platform to deploy from GitHub
3. Configure environment variables
4. Deploy with automatic scaling

### 6. **AWS Elastic Beanstalk**
**Pros:** Highly scalable, AWS ecosystem, robust
**Cons:** More complex setup, can be expensive

### 7. **Google Cloud Run**
**Pros:** Pay-per-use, highly scalable, good free tier
**Cons:** Requires containerization knowledge

## 🐳 Docker Deployment

### Using Docker Hub + Any Platform

1. **Build and push Docker image:**
```bash
# Build image
docker build -t your-username/ctf-sql-injection .

# Push to Docker Hub
docker push your-username/ctf-sql-injection
```

2. **Deploy on any platform supporting Docker:**
```bash
# Run container
docker run -d \
  -p 3000:3000 \
  -e MONGODB_URI="your_mongodb_uri" \
  -e FLAG="your_flag" \
  your-username/ctf-sql-injection
```

## 🔧 Environment Variables Setup

### Required Environment Variables:
```bash
MONGODB_URI=mongodb+srv://tamils343435:<EMAIL>/ctf_bank?retryWrites=true&w=majority&appName=Cluster0
FLAG=WOLF{your_custom_flag_here}
PORT=3000
```

### Platform-Specific Setup:

#### Heroku:
```bash
heroku config:set MONGODB_URI="your_uri"
heroku config:set FLAG="your_flag"
```

#### Vercel:
- Go to Vercel Dashboard → Project → Settings → Environment Variables
- Add each variable

#### Railway:
```bash
railway variables set MONGODB_URI="your_uri"
railway variables set FLAG="your_flag"
```

## 🚀 Quick Deployment Script

Create `deploy.js`:
```javascript
const { execSync } = require('child_process');
const fs = require('fs');

console.log('🚀 CTF Challenge Deployment Script');

// Check if .env exists
if (!fs.existsSync('.env')) {
    console.error('❌ .env file not found. Please create it first.');
    process.exit(1);
}

// Read environment variables
require('dotenv').config();

if (!process.env.FLAG) {
    console.error('❌ FLAG environment variable not set.');
    process.exit(1);
}

console.log('✅ Environment variables validated');

// Choose deployment platform
const platform = process.argv[2] || 'heroku';

try {
    switch (platform) {
        case 'heroku':
            console.log('🔄 Deploying to Heroku...');
            execSync('git add .', { stdio: 'inherit' });
            execSync('git commit -m "Deploy CTF challenge"', { stdio: 'inherit' });
            execSync('git push heroku main', { stdio: 'inherit' });
            break;
            
        case 'vercel':
            console.log('🔄 Deploying to Vercel...');
            execSync('vercel --prod', { stdio: 'inherit' });
            break;
            
        case 'railway':
            console.log('🔄 Deploying to Railway...');
            execSync('railway up', { stdio: 'inherit' });
            break;
            
        default:
            console.log('❌ Unknown platform. Supported: heroku, vercel, railway');
    }
    
    console.log('✅ Deployment completed!');
} catch (error) {
    console.error('❌ Deployment failed:', error.message);
    process.exit(1);
}
```

## 📊 Performance Optimization

### 1. **Database Connection Pooling**
```javascript
// In server.js
mongoose.connect(MONGODB_URI, {
    useNewUrlParser: true,
    useUnifiedTopology: true,
    maxPoolSize: 10, // Maintain up to 10 socket connections
    serverSelectionTimeoutMS: 5000, // Keep trying to send operations for 5 seconds
    socketTimeoutMS: 45000, // Close sockets after 45 seconds of inactivity
});
```

### 2. **Caching**
```javascript
// Add simple in-memory cache
const cache = new Map();

app.get('/api/users', async (req, res) => {
    const cacheKey = 'users';
    
    if (cache.has(cacheKey)) {
        return res.json(cache.get(cacheKey));
    }
    
    const users = await User.find({}, { password: 0 });
    cache.set(cacheKey, { users });
    
    // Cache for 5 minutes
    setTimeout(() => cache.delete(cacheKey), 5 * 60 * 1000);
    
    res.json({ users });
});
```

### 3. **Compression**
```javascript
const compression = require('compression');
app.use(compression());
```

## 🔒 Security for Production

### 1. **Environment Variables**
- Never commit `.env` files
- Use platform-specific environment variable systems
- Rotate flags regularly

### 2. **Rate Limiting**
```javascript
const rateLimit = require('express-rate-limit');

const limiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100 // limit each IP to 100 requests per windowMs
});

app.use('/api/', limiter);
```

### 3. **HTTPS Enforcement**
```javascript
app.use((req, res, next) => {
    if (req.header('x-forwarded-proto') !== 'https') {
        res.redirect(`https://${req.header('host')}${req.url}`);
    } else {
        next();
    }
});
```

## 📈 Monitoring and Analytics

### 1. **Basic Logging**
```javascript
const winston = require('winston');

const logger = winston.createLogger({
    level: 'info',
    format: winston.format.json(),
    transports: [
        new winston.transports.File({ filename: 'error.log', level: 'error' }),
        new winston.transports.File({ filename: 'combined.log' })
    ]
});

// Log flag access attempts
app.post('/api/login', async (req, res) => {
    const { username } = req.body;
    
    if (username.toLowerCase().includes('union')) {
        logger.info('Flag access attempt', {
            ip: req.ip,
            userAgent: req.get('User-Agent'),
            payload: username,
            timestamp: new Date()
        });
    }
    
    // ... rest of login logic
});
```

### 2. **Health Check Endpoint**
```javascript
app.get('/health', (req, res) => {
    res.json({
        status: 'OK',
        timestamp: new Date(),
        uptime: process.uptime(),
        mongodb: mongoose.connection.readyState === 1 ? 'connected' : 'disconnected'
    });
});
```

## 🎯 Recommended Setup for CTF

**For Small CTF (< 100 participants):**
- Platform: Heroku or Railway
- Database: MongoDB Atlas (free tier)
- Monitoring: Basic logging

**For Medium CTF (100-500 participants):**
- Platform: Vercel or Render
- Database: MongoDB Atlas (paid tier)
- Monitoring: Winston + external service

**For Large CTF (500+ participants):**
- Platform: AWS/GCP with load balancing
- Database: MongoDB Atlas cluster
- Monitoring: Full observability stack

Choose the platform that best fits your needs, budget, and technical expertise!
